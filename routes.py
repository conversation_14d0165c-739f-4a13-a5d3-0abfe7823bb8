from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jin<PERSON>2Templates
from services import get_general_financial_advice, get_stock_advice
from schemas import ChatMessage, ChatResponse

router = APIRouter()
templates = Jinja2Templates(directory="templates")

@router.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@router.post("/chat", response_model=ChatResponse)
async def chat(chat_message: ChatMessage, request: Request):
    message = chat_message.message
    # Simple logic to differentiate between general questions and stock advice
    if "stock" in message.lower() or "advice on" in message.lower():
        # Extract stock name (this is a simplistic approach)
        try:
            stock_name = message.split("advice on")[1].strip()
        except IndexError:
            stock_name = message.split("stock")[1].strip()
        
        response = get_stock_advice(stock_name)
    else:
        response = get_general_financial_advice(message)
    
    return ChatResponse(response=response)
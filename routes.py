
from fastapi import APIRouter, Request, Form
from fastapi.responses import HTMLResponse
from fastapi.templating import Jin<PERSON>2Templates
from services import get_general_financial_advice, get_stock_advice

router = APIRouter()
templates = Jinja2Templates(directory="templates")

@router.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@router.post("/chat", response_class=HTMLResponse)
async def chat(request: Request, message: str = Form(...)):
    # Simple logic to differentiate between general questions and stock advice
    if "stock" in message.lower() or "advice on" in message.lower():
        # Extract stock name (this is a simplistic approach)
        try:
            stock_name = message.split("advice on")[1].strip()
        except IndexError:
            stock_name = message.split("stock")[1].strip()
        
        response = get_stock_advice(stock_name)
    else:
        response = get_general_financial_advice(message)
    
    return templates.TemplateResponse("index.html", {"request": request, "response": response, "message": message})

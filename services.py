
import os
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.prompts import Chat<PERSON>romptTemplate
from langchain.schema.output_parser import StrOutputParser
from dotenv import load_dotenv
from firecrawl import Firecrawl

load_dotenv()

# Set up the Google Generative AI model
llm = ChatGoogleGenerativeAI(model="gemini_pro", api_key=os.getenv("GOOGLE_API_KEY"))
firecrawl = Firecrawl(api_key=os.getenv("FIRECRAWL_API_KEY"))

def get_general_financial_advice(question: str):
    """
    Provides general financial advice using a language model.
    """
    prompt_template = ChatPromptTemplate.from_messages(
        [
            ("system", """
            You are an expert financial advisor. Your goal is to provide comprehensive, unbiased, and educational financial advice.
            When answering the user's question, please consider the following:
            - **Risk Tolerance:** Briefly explain the importance of risk tolerance and how it might influence the advice.
            - **Time Horizon:** Mention how the user's time horizon (short-term vs. long-term) can affect their financial decisions.
            - **Diversification:** Explain the concept of diversification and its role in managing risk.
            - **Disclaimer:** Always include a disclaimer that your advice is for informational purposes only and that the user should consult with a qualified financial professional before making any investment decisions.
            """
            ),
            ("user", "{question}"),
        ]
    )
    chain = prompt_template | llm | StrOutputParser()
    return chain.invoke({"question": question})

def get_stock_advice(stock_name: str):
    """
    Provides advice on specific Nigerian stocks using Firecrawl.
    """
    url = f"https://africanfinancials.com/company/ng-{stock_name.lower()}/"
    try:
        doc = firecrawl.scrape(url, formats=["markdown"])
        prompt_template = ChatPromptTemplate.from_messages(
            [
                ("system", """
                You are a highly experienced stock market analyst specializing in the Nigerian stock market.
                You will be given the markdown content of a webpage containing financial information about a specific company.
                Your task is to perform a detailed analysis of the provided data and generate a comprehensive investment recommendation.

                Please structure your analysis as follows:
                1.  **Company Overview:** Briefly describe the company's business and its industry.
                2.  **Financial Highlights:** Identify and summarize key financial metrics from the document (e.g., revenue, profit, P/E ratio, dividend yield).
                3.  **Recent Performance:** Analyze the company's recent stock performance and any notable news or events.
                4.  **Investment Thesis:** Based on your analysis, provide a clear investment thesis. State whether you recommend buying, selling, or holding the stock and provide a well-reasoned justification for your recommendation.
                5.  **Risk Assessment:** Identify and discuss the potential risks associated with investing in this stock.
                6.  **Disclaimer:** Include a disclaimer that this is not financial advice and that the user should do their own research.
                """
                ),
                ("user", "{document}"),
            ]
        )
        chain = prompt_template | llm | StrOutputParser()
        return chain.invoke({"document": doc['markdown']})
    except Exception as e:
        return f"Could not retrieve data for {stock_name}. Error: {e}"

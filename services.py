
import os
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.prompts import Chat<PERSON>romptTemplate
from langchain.schema.output_parser import StrOutputParser
from dotenv import load_dotenv

load_dotenv()

# Set up the Google Generative AI model
llm = ChatGoogleGenerativeAI(model="gemini_pro", api_key=os.getenv("GOOGLE_API_KEY"))

def get_general_financial_advice(question: str):
    """
    Provides general financial advice using a language model.
    """
    prompt_template = ChatPromptTemplate.from_messages(
        [
            ("system", "You are a helpful financial assistant. Provide informative and educational answers to the following question."),
            ("user", "{question}"),
        ]
    )
    chain = prompt_template | llm | StrOutputParser()
    return chain.invoke({"question": question})

def get_stock_advice(stock_name: str):
    """
    Placeholder function for providing advice on specific Nigerian stocks.
    This will be implemented later with web scraping.
    """
    # Replace this with actual web scraping and analysis
    return f"Advice for {stock_name} is not available yet. This feature is under development."

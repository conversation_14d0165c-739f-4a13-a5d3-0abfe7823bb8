<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finance Chatbot</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-4">
        <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-md">
            <div class="p-4 border-b">
                <h1 class="text-2xl font-bold">Finance Chatbot</h1>
            </div>
            <div id="chat-box" class="p-4 h-96 overflow-y-auto">
                <div class="mb-4">
                    <div class="bg-blue-500 text-white rounded-lg p-2 self-start">Welcome to the Finance Chatbot! Ask me about finances or Nigerian stocks.</div>
                </div>
            </div>
            <div class="p-4 border-t">
                <form id="chat-form" class="flex">
                    <input type="text" id="message" class="flex-grow rounded-l-lg p-2 border" placeholder="Type your message...">
                    <button type="submit" class="bg-blue-500 text-white rounded-r-lg px-4">Send</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('chat-form').addEventListener('submit', async function(event) {
            event.preventDefault();
            const messageInput = document.getElementById('message');
            const message = messageInput.value;
            messageInput.value = '';

            const chatBox = document.getElementById('chat-box');

            // Display user message
            const userMessageDiv = document.createElement('div');
            userMessageDiv.className = 'mb-4';
            userMessageDiv.innerHTML = `<div class="bg-gray-300 rounded-lg p-2 self-end">${message}</div>`;
            chatBox.appendChild(userMessageDiv);

            const response = await fetch('/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ message: message })
            });

            const data = await response.json();

            // Display bot response
            const botMessageDiv = document.createElement('div');
            botMessageDiv.className = 'mb-4';
            botMessageDiv.innerHTML = `<div class="bg-blue-500 text-white rounded-lg p-2 self-start">${data.response}</div>`;
            chatBox.appendChild(botMessageDiv);

            chatBox.scrollTop = chatBox.scrollHeight;
        });
    </script>
</body>
</html>